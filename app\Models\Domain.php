<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model as Eloquent;

class Domain extends Eloquent
{
    protected $connection = 'mongodb';
    protected $collection = 'domains';

    protected $fillable = [
        'salon_id',
        'custom_domain',
        'subdomain',
        'ssl_certificates',
    ];

    protected $casts = [
        'salon_id' => 'objectId',
        'ssl_certificates' => 'boolean',
    ];

    // Relations

    /**
     * Salon associé au domaine personnalisé.
     */
    public function salon()
    {
        return $this->belongsTo(Salon::class, 'salon_id', '_id');
    }
}
