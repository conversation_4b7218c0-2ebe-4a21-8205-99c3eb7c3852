<?php

namespace Tests\Feature;

use Carbon\Carbon;
use Tests\TestCase;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Notification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Notifications\EmailVerificationNotification;

class AuthTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Tester l'inscription d'un nouvel utilisateur et la réception du code de vérification par email.
     */
    public function test_user_can_register_and_receive_verification_code()
    {
        Notification::fake();

        $response = $this->postJson('/api/register', [
            'role' => 'client',
            'profile' => [
                'title' => 'Monsieur',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'email' => '<EMAIL>',
                'phone' => '1234567890',
                'gender' => 'male',
            ],
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $response->assertStatus(201)
                 ->assertJson([
                     'message' => 'Inscription réussie. Un code de vérification a été envoyé à votre adresse email.',
                 ]);

        $user = User::where('profile.email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertNull($user->email_verified_at);

        Notification::assertSentTo(
            [$user],
            EmailVerificationNotification::class
        );
    }

    /**
     * Tester la vérification de l'email avec un code valide.
     */
    public function test_user_can_verify_email_with_valid_code()
    {
        Notification::fake();

        $user = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'verification_code' => '123456',
            'verification_code_expires_at' => Carbon::now()->addMinutes(10),
        ]);

        $response = $this->postJson('/api/verify-email', [
            'email' => '<EMAIL>',
            'verification_code' => '123456',
        ]);

        $response->assertStatus(200)
                 ->assertJson([
                     'message' => 'Email vérifié avec succès.',
                 ]);

        $user->refresh();
        $this->assertNotNull($user->email_verified_at);
        $this->assertNull($user->verification_code);
        $this->assertNull($user->verification_code_expires_at);
    }

    /**
     * Tester la vérification de l'email avec un code invalide.
     */
    public function test_user_cannot_verify_email_with_invalid_code()
    {
        Notification::fake();

        $user = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'verification_code' => '123456',
            'verification_code_expires_at' => Carbon::now()->addMinutes(10),
        ]);

        $response = $this->postJson('/api/verify-email', [
            'email' => '<EMAIL>',
            'verification_code' => '654321',
        ]);

        $response->assertStatus(400)
                 ->assertJson([
                     'message' => 'Code de vérification invalide.',
                 ]);

        $user->refresh();
        $this->assertNull($user->email_verified_at);
    }

    /**
     * Tester la vérification de l'email avec un code expiré.
     */
    public function test_user_cannot_verify_email_with_expired_code()
    {
        Notification::fake();

        $user = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'verification_code' => '123456',
            'verification_code_expires_at' => Carbon::now()->subMinutes(1),
        ]);

        $response = $this->postJson('/api/verify-email', [
            'email' => '<EMAIL>',
            'verification_code' => '123456',
        ]);

        $response->assertStatus(400)
                 ->assertJson([
                     'message' => 'Le code de vérification a expiré.',
                 ]);

        $user->refresh();
        $this->assertNull($user->email_verified_at);
    }

    /**
     * Tester la connexion d'un utilisateur avec des informations d'identification valides.
     */
    public function test_user_can_login_with_valid_credentials()
    {
        $user = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'email_verified_at' => now(),
        ]);

        $response = $this->postJson('/api/login', [
            'profile.email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'access_token',
                     'token_type',
                 ]);
    }

    /**
     * Tester la connexion d'un utilisateur avec des informations d'identification invalides.
     */
    public function test_user_cannot_login_with_invalid_credentials()
    {
        $user = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'email_verified_at' => now(),
        ]);

        $response = $this->postJson('/api/login', [
            'profile.email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['profile.email']);
    }

    /**
     * Tester la connexion d'un utilisateur non vérifié.
     */
    public function test_user_cannot_login_if_email_not_verified()
    {
        $user = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'email_verified_at' => null,
        ]);

        $response = $this->postJson('/api/login', [
            'profile.email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $response->assertStatus(403)
                 ->assertJson([
                     'message' => 'Veuillez vérifier votre adresse email avant de vous connecter.',
                 ]);
    }

    /**
     * Tester la déconnexion d'un utilisateur.
     */
    public function test_user_can_logout()
    {
        $user = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'email_verified_at' => now(),
        ]);

        // Se connecter pour obtenir un token
        $token = $user->createToken('auth_token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/logout');

        $response->assertStatus(200)
                 ->assertJson([
                     'message' => 'Déconnecté avec succès.',
                 ]);

        // Vérifier que le token est supprimé
        $this->assertDatabaseMissing('personal_access_tokens', [
            'tokenable_id' => $user->_id,
            'tokenable_type' => get_class($user),
        ]);
    }

    /**
     * Tester la récupération du profil de l'utilisateur.
     */
    public function test_user_can_retrieve_profile()
    {
        $user = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'email_verified_at' => now(),
        ]);

        $token = $user->createToken('auth_token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/profile');

        $response->assertStatus(200)
                 ->assertJson([
                     'profile' => [
                         'email' => '<EMAIL>',
                         // Autres champs du profil...
                     ],
                     // Autres champs de l'utilisateur...
                 ]);
    }

    /**
     * Tester la mise à jour du profil de l'utilisateur.
     */
    public function test_user_can_update_profile()
    {
        $user = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'email_verified_at' => now(),
            'profile.first_name' => 'John',
            'profile.last_name' => 'Doe',
        ]);

        $token = $user->createToken('auth_token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->putJson('/api/profile', [
            'profile' => [
                'first_name' => 'Jane',
                'last_name' => 'Smith',
            ],
            'password' => 'newpassword123',
            'password_confirmation' => 'newpassword123',
        ]);

        $response->assertStatus(200)
                 ->assertJson([
                     'message' => 'Profil mis à jour avec succès.',
                     'user' => [
                         'profile' => [
                             'first_name' => 'Jane',
                             'last_name' => 'Smith',
                         ],
                     ],
                 ]);

        $user->refresh();
        $this->assertEquals('Jane', $user->profile['first_name']);
        $this->assertEquals('Smith', $user->profile['last_name']);
        $this->assertTrue(Hash::check('newpassword123', $user->password));
    }

    /**
     * Tester la restriction d'accès aux routes protégées sans authentification.
     */
    public function test_protected_routes_require_authentication()
    {
        $response = $this->getJson('/api/profile');

        $response->assertStatus(401)
                 ->assertJson([
                     'message' => 'Unauthenticated.',
                 ]);
    }
}
