<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model as Eloquent;

class Reservation extends Eloquent
{
    protected $connection = 'mongodb';
    protected $collection = 'reservations';

    protected $fillable = [
        'client_id',
        'salon_id',
        'coiffeur_id',
        'services',
        'date_time',
        'status',
        'total_time',
    ];

    protected $casts = [
        'services' => 'array',
        'date_time' => 'datetime',
        'total_time' => 'integer',
    ];

    // Relations

    /**
     * Client qui a effectué la réservation.
     */
    public function client()
    {
        return $this->belongsTo(User::class, 'client_id', '_id');
    }

    /**
     * Salon où la réservation a été effectuée.
     */
    public function salon()
    {
        return $this->belongsTo(Salon::class, 'salon_id', '_id');
    }

    /**
     * Coiffeur attribué à la réservation.
     */
    public function coiffeur()
    {
        return $this->belongsTo(User::class, 'coiffeur_id', '_id');
    }

    /**
     * Services réservés.
     */
    public function servicesDetails()
    {
        return $this->belongsToMany(Service::class, 'services', 'service_id', '_id');
    }
}
