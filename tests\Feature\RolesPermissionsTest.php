<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;

class RolesPermissionsTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Tester que les rôles sont correctement assignés aux utilisateurs.
     */
    public function test_roles_are_assigned_correctly()
    {
        // Créer des rôles et permissions
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);

        // Créer un owner
        $owner = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'role' => 'owner',
        ]);
        $owner->assignRole('owner');

        // Créer un manager
        $manager = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'role' => 'manager',
        ]);
        $manager->assignRole('manager');

        // Créer un stylist
        $stylist = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'role' => 'stylist',
        ]);
        $stylist->assignRole('stylist');

        // Créer un client
        $client = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'role' => 'client',
        ]);
        $client->assignRole('client');

        // Vérifier les rôles
        $this->assertTrue($owner->hasRole('owner'));
        $this->assertTrue($manager->hasRole('manager'));
        $this->assertTrue($stylist->hasRole('stylist'));
        $this->assertTrue($client->hasRole('client'));
    }

    /**
     * Tester que les permissions sont attribuées correctement aux rôles.
     */
    public function test_permissions_are_assigned_correctly_to_roles()
    {
        // Créer des rôles et permissions
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);

        // Récupérer les rôles
        $ownerRole = Role::findByName('owner');
        $managerRole = Role::findByName('manager');
        $stylistRole = Role::findByName('stylist');
        $clientRole = Role::findByName('client');

        // Vérifier que le owner a toutes les permissions
        $this->assertCount(15, $ownerRole->permissions);

        // Vérifier les permissions du manager
        $this->assertTrue($managerRole->hasPermissionTo('manage salons'));
        $this->assertTrue($managerRole->hasPermissionTo('manage services'));
        $this->assertTrue($managerRole->hasPermissionTo('manage reservations'));
        $this->assertTrue($managerRole->hasPermissionTo('manage reviews'));
        $this->assertTrue($managerRole->hasPermissionTo('manage themes'));
        $this->assertTrue($managerRole->hasPermissionTo('manage domains'));
        $this->assertTrue($managerRole->hasPermissionTo('manage timeslots'));
        $this->assertTrue($managerRole->hasPermissionTo('view profile'));
        $this->assertTrue($managerRole->hasPermissionTo('update profile'));

        // Vérifier les permissions du stylist
        $this->assertTrue($stylistRole->hasPermissionTo('manage reservations'));
        $this->assertTrue($stylistRole->hasPermissionTo('view profile'));
        $this->assertTrue($stylistRole->hasPermissionTo('update profile'));
        $this->assertTrue($stylistRole->hasPermissionTo('manage timeslots'));

        // Vérifier les permissions du client
        $this->assertTrue($clientRole->hasPermissionTo('view profile'));
        $this->assertTrue($clientRole->hasPermissionTo('update profile'));
        $this->assertTrue($clientRole->hasPermissionTo('create reservations'));
        $this->assertTrue($clientRole->hasPermissionTo('view reservations'));
        $this->assertTrue($clientRole->hasPermissionTo('cancel reservations'));
        $this->assertTrue($clientRole->hasPermissionTo('reschedule reservations'));
    }

    /**
     * Tester qu'un utilisateur sans la permission appropriée ne peut pas accéder à une route protégée.
     */
    public function test_user_without_permission_cannot_access_protected_route()
    {
        // Créer des rôles et permissions
        $this->seed(\Database\Seeders\RolesAndPermissionsSeeder::class);

        // Créer un client sans permissions de gestion
        $client = User::factory()->create([
            'profile.email' => '<EMAIL>',
            'role' => 'client',
        ]);
        $client->assignRole('client');

        // Tenter d'accéder à une route protégée (par exemple, gestion des salons)
        $token = $client->createToken('auth_token')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/admin/salons');

        $response->assertStatus(403); // Forbidden
    }
}
