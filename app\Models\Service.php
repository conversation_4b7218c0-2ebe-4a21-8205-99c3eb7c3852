<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model as Eloquent;

class Service extends Eloquent
{
    protected $connection = 'mongodb';
    protected $collection = 'services';

    protected $fillable = [
        'name',
        'description',
        'duration',
        'price',
    ];

    protected $casts = [
        'duration' => 'integer',
        'price' => 'float',
    ];

    // Relations

    /**
     * Salons qui proposent ce service.
     */
    public function salons()
    {
        return $this->belongsToMany(Salon::class, 'salons', 'service_id', '_id');
    }

    /**
     * Réservations incluant ce service.
     */
    public function reservations()
    {
        return $this->belongsToMany(Reservation::class, 'reservations', 'service_id', '_id');
    }
}
