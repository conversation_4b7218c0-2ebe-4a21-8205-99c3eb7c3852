<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UsersTableSeeder extends Seeder
{
    /**
     * Exécuter les seeds pour les utilisateurs.
     *
     * @return void
     */
    public function run()
    {
        // Créer un Owner
        $owner = User::firstOrCreate([
            'profile.email' => '<EMAIL>',
        ], [
            'role' => 'owner',
            'profile.title' => 'Monsieur',
            'profile.first_name' => 'Owner',
            'profile.last_name' => 'Salon',
            'profile.phone' => '0123456789',
            'profile.gender' => 'male',
            'password' => Hash::make('password123'),
        ]);

        $owner->assignRole('owner');

        // Créer un Manager
        $manager = User::firstOrCreate([
            'profile.email' => '<EMAIL>',
        ], [
            'role' => 'manager',
            'profile.title' => 'Madame',
            'profile.first_name' => 'Manager',
            'profile.last_name' => 'Salon',
            'profile.phone' => '0987654321',
            'profile.gender' => 'female',
            'password' => Hash::make('password123'),
        ]);

        $manager->assignRole('manager');

        // Créer un Stylist
        $stylist = User::firstOrCreate([
            'profile.email' => '<EMAIL>',
        ], [
            'role' => 'stylist',
            'profile.title' => 'Monsieur',
            'profile.first_name' => 'Stylist',
            'profile.last_name' => 'Coiffure',
            'profile.phone' => '0112233445',
            'profile.gender' => 'male',
            'password' => Hash::make('password123'),
        ]);

        $stylist->assignRole('stylist');

        // Créer un Client
        $client = User::firstOrCreate([
            'profile.email' => '<EMAIL>',
        ], [
            'role' => 'client',
            'profile.title' => 'Madame',
            'profile.first_name' => 'Client',
            'profile.last_name' => 'User',
            'profile.phone' => '0555666777',
            'profile.gender' => 'female',
            'password' => Hash::make('password123'),
        ]);

        $client->assignRole('client');
    }
}
