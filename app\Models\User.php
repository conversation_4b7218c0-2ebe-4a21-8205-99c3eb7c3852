<?php

namespace App\Models;

// use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model as Eloquent;
// use Illuminate\Foundation\Auth\User as Authenticatable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Spa<PERSON>\Permission\Traits\HasRoles;
use Illuminate\Notifications\Notifiable;
use MongoDB\Laravel\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class User extends Authenticatable
{
    use HasApiTokens, HasRoles, Notifiable, HasFactory;

    protected $connection = 'mongodb';
    protected $collection = 'users';

    protected $fillable = [
        'role',
        'profile.title',
        'profile.first_name',
        'profile.last_name',
        'profile.email',
        'profile.phone',
        'profile.photo',
        'profile.gender',
        'password',
        'email_verified_at',
        'verification_code',
        'verification_code_expires_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
        'verification_code',
    ];

    protected $casts = [
        'profile' => 'array',
        'email_verified_at' => 'datetime',
        'verification_code_expires_at' => 'datetime',
    ];

    // Relations

    /**
     * Les salons possédés par l'utilisateur (si propriétaire).
     */
    public function salons()
    {
        return $this->hasMany(Salon::class, 'owner_id', '_id');
    }

    /**
     * Les réservations effectuées par l'utilisateur (si client).
     */
    public function reservations()
    {
        return $this->hasMany(Reservation::class, 'client_id', '_id');
    }

    /**
     * Les réservations attribuées à l'utilisateur (si coiffeur).
     */
    public function assignedReservations()
    {
        return $this->hasMany(Reservation::class, 'coiffeur_id', '_id');
    }

    /**
    * Salons gérés par l'utilisateur (si propriétaire ou manager).
    */
    public function managedSalons()
    {
        return $this->hasMany(Salon::class, 'manager_id', '_id');
    }


}
