<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition()
    {
        return [
            'role' => 'client',
            'profile.title' => $this->faker->randomElement(['Monsieur', 'Madame']),
            'profile.first_name' => $this->faker->firstName,
            'profile.last_name' => $this->faker->lastName,
            'profile.email' => $this->faker->unique()->safeEmail,
            'profile.phone' => $this->faker->phoneNumber,
            'profile.gender' => $this->faker->randomElement(['male', 'female', 'other']),
            'password' => Hash::make('password123'), // Mot de passe par défaut
            'email_verified_at' => null,
            'verification_code' => rand(100000, 999999),
            'verification_code_expires_at' => now()->addMinutes(10),
        ];
    }

    /**
     * Indiquer que l'utilisateur a un email vérifié.
     */
    public function verified()
    {
        return $this->state([
            'email_verified_at' => now(),
            'verification_code' => null,
            'verification_code_expires_at' => null,
        ]);
    }
}
