<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use App\Notifications\EmailVerificationNotification;

class AuthController extends Controller
{
    /**
     * Inscription d'un nouvel utilisateur et envoi du code de vérification.
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'role' => 'required|in:owner,manager,stylist,client',
            'profile.title' => 'required|string|in:Monsieur,Madame',
            'profile.first_name' => 'required|string|max:255',
            'profile.last_name' => 'required|string|max:255',
            'profile.email' => 'required|string|email|unique:users,profile.email',
            'profile.phone' => 'required|string|max:20',
            'profile.gender' => 'required|string|in:male,female,other',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        // Générer un code de vérification aléatoire
        $verification_code = rand(100000, 999999);

        // Créer l'utilisateur
        $user = User::create([
            'role' => $request->role,
            'profile.title' => $request->input('profile.title'),
            'profile.first_name' => $request->input('profile.first_name'),
            'profile.last_name' => $request->input('profile.last_name'),
            'profile.email' => $request->input('profile.email'),
            'profile.phone' => $request->input('profile.phone'),
            'profile.gender' => $request->input('profile.gender'),
            'password' => Hash::make($request->password),
            'verification_code' => $verification_code,
            'verification_code_expires_at' => Carbon::now()->addMinutes(10),
        ]);

        // Assigner le rôle
        $user->assignRole($request->role);

        // Envoyer la notification par email
        $user->notify(new EmailVerificationNotification($verification_code));

        return response()->json([
            'message' => 'Inscription réussie. Un code de vérification a été envoyé à votre adresse email.',
        ], 201);
    }

    /**
     * Vérifier le code d'email.
     */
    public function verifyEmail(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|exists:users,profile.email',
            'verification_code' => 'required|digits:6',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $user = User::where('profile.email', $request->email)->first();

        if ($user->email_verified_at) {
            return response()->json(['message' => 'Email déjà vérifié.'], 400);
        }

        if ($user->verification_code !== $request->verification_code) {
            return response()->json(['message' => 'Code de vérification invalide.'], 400);
        }

        if (Carbon::now()->greaterThan($user->verification_code_expires_at)) {
            return response()->json(['message' => 'Le code de vérification a expiré.'], 400);
        }

        // Marquer l'email comme vérifié
        $user->email_verified_at = Carbon::now();
        $user->verification_code = null;
        $user->verification_code_expires_at = null;
        $user->save();

        return response()->json(['message' => 'Email vérifié avec succès.'], 200);
    }

    /**
     * Connexion de l'utilisateur.
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'profile.email' => 'required|string|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $user = User::where('profile.email', $request->input('profile.email'))->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'profile.email' => ['Les informations d\'identification sont incorrectes.'],
            ]);
        }

        if (!$user->email_verified_at) {
            return response()->json(['message' => 'Veuillez vérifier votre adresse email avant de vous connecter.'], 403);
        }

        $token = $user->createToken('auth_token')->plainTextToken;

        return response()->json([
            'access_token' => $token,
            'token_type' => 'Bearer',
        ], 200);
    }

    /**
     * Déconnexion de l'utilisateur.
     */
    public function logout(Request $request)
    {
        $request->user()->currentAccessToken()->delete();

        return response()->json([
            'message' => 'Déconnecté avec succès.'
        ], 200);
    }

    /**
     * Récupérer le profil de l'utilisateur.
     */
    public function profile(Request $request)
    {
        return response()->json($request->user());
    }

    /**
     * Mettre à jour le profil de l'utilisateur.
     */
    public function updateProfile(Request $request)
    {
        $user = $request->user();

        $validator = Validator::make($request->all(), [
            'profile.first_name' => 'sometimes|required|string|max:255',
            'profile.last_name' => 'sometimes|required|string|max:255',
            'profile.phone' => 'sometimes|required|string|max:20',
            'profile.gender' => 'sometimes|required|string|in:male,female,other',
            'profile.photo' => 'sometimes|nullable|image|max:2048',
            'password' => 'sometimes|nullable|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        if ($request->filled('password')) {
            $user->password = Hash::make($request->password);
        }

        if ($request->hasFile('profile.photo')) {
            // Stocker la photo et mettre à jour le chemin
            $path = $request->file('profile.photo')->store('photos', 'public');
            $user->profile['photo'] = $path;
        }

        // Mettre à jour les autres champs du profil
        $user->profile = array_merge($user->profile, $request->input('profile', []));

        $user->save();

        return response()->json([
            'message' => 'Profil mis à jour avec succès.',
            'user' => $user
        ], 200);
    }

    /**
     * Renvoi du code de vérification par email.
     */
    public function resendVerificationCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|string|email|exists:users,profile.email',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        $user = User::where('profile.email', $request->email)->first();

        if ($user->email_verified_at) {
            return response()->json(['message' => 'Email déjà vérifié.'], 400);
        }

        // Générer un nouveau code de vérification
        $verification_code = rand(100000, 999999);
        $user->verification_code = $verification_code;
        $user->verification_code_expires_at = Carbon::now()->addMinutes(10);
        $user->save();

        // Envoyer la notification par email
        $user->notify(new EmailVerificationNotification($verification_code));

        return response()->json([
            'message' => 'Un nouveau code de vérification a été envoyé à votre adresse email.',
        ], 200);
    }
}
