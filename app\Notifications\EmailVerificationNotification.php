<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class EmailVerificationNotification extends Notification
{
    use Queueable;

    protected $code;

    /**
     * Create a new notification instance.
     *
     * @param string $code
     */
    public function __construct($code)
    {
        $this->code = $code;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
                    ->subject('Vérification de votre adresse email')
                    ->greeting('Bonjour ' . $notifiable->profile['first_name'] . ',')
                    ->line('Merci de vous être inscrit sur notre plateforme.')
                    ->line('Votre code de vérification est : ' . $this->code)
                    ->line('Ce code est valable pendant 10 minutes.')
                    ->action('Vérifier Email', url('/'))
                    ->line('Si vous n\'avez pas créé de compte, aucune action supplémentaire n\'est requise.');
    }
}
