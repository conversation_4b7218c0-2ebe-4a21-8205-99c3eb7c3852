<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model as Eloquent;

class Theme extends Eloquent
{
    protected $connection = 'mongodb';
    protected $collection = 'themes';

    protected $fillable = [
        'name',
        'css_variables',
    ];

    protected $casts = [
        'css_variables' => 'array',
    ];

    // Relations

    /**
     * Salons utilisant ce thème.
     */
    public function salons()
    {
        return $this->hasMany(Salon::class, 'theme_id', '_id');
    }
}
