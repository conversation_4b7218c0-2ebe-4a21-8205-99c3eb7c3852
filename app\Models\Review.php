<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model as Eloquent;

class Review extends Eloquent
{
    protected $connection = 'mongodb';
    protected $collection = 'reviews';

    protected $fillable = [
        'salon_id',
        'client_id',
        'rating',
        'comment',
        'source',
    ];

    protected $casts = [
        'rating' => 'integer',
        'salon_id' => 'objectId',
        'client_id' => 'objectId',
    ];

    // Relations

    /**
     * Salon associé à l'avis.
     */
    public function salon()
    {
        return $this->belongsTo(Salon::class, 'salon_id', '_id');
    }

    /**
     * Client qui a laissé l'avis.
     */
    public function client()
    {
        return $this->belongsTo(User::class, 'client_id', '_id');
    }
}
