<?php

namespace App\Models;

use MongoDB\Laravel\Eloquent\Model as Eloquent;

class Salon extends Eloquent
{
    protected $connection = 'mongodb';
    protected $collection = 'salons';

    protected $fillable = [
        'owner_id',
        'name',
        'photo',
        'description',
        'social_links.tiktok',
        'social_links.instagram',
        'services',
        'coiffeurs',
        'address.street',
        'address.city',
        'address.zipcode',
        'address.country',
        'hours',
        'contact_info.email',
        'contact_info.phone',
    ];

    protected $casts = [
        'services' => 'array',
        'coiffeurs' => 'array',
        'social_links' => 'array',
        'address' => 'array',
        'hours' => 'array',
        'contact_info' => 'array',
    ];

    // Relations

    /**
     * Propriétaire du salon.
     */
    public function owner()
    {
        return $this->belongsTo(User::class, 'owner_id', '_id');
    }

    /**
     * Coiffeurs travaillant dans le salon.
     */
    public function coiffeurs()
    {
        return $this->belongsToMany(User::class, 'coiffeurs', 'salon_id', 'user_id');
    }

    /**
     * Services proposés par le salon.
     */
    public function services()
    {
        return $this->belongsToMany(Service::class, 'services', 'salon_id', '_id');
    }

    /**
     * Réservations associées au salon.
     */
    public function reservations()
    {
        return $this->hasMany(Reservation::class, 'salon_id', '_id');
    }

    /**
     * Avis du salon.
     */
    public function reviews()
    {
        return $this->hasMany(Review::class, 'salon_id', '_id');
    }

    /**
     * Domaine personnalisé du salon.
     */
    public function domain()
    {
        return $this->hasOne(Domain::class, 'salon_id', '_id');
    }

    /**
    * Thème utilisé par le salon.
    */
    public function theme()
    {
        return $this->belongsTo(Theme::class, 'theme_id', '_id');
    }

    /**
    * Ajouter un coiffeur au salon.
    */
    public function addCoiffeur(User $coiffeur)
    {
        $this->coiffeurs()->attach($coiffeur->_id);
    }

    /**
    * Retirer un coiffeur du salon.
    */
    public function removeCoiffeur(User $coiffeur)
    {
        $this->coiffeurs()->detach($coiffeur->_id);
    }

}
