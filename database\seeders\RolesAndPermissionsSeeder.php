<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Exécuter les seeds pour les rôles et les permissions.
     *
     * @return void
     */
    public function run()
    {
        // Réinitialiser les caches de Spatie
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Définir les permissions
        $permissions = [
            // Gestion des utilisateurs
            'manage users',

            // Gestion des salons
            'manage salons',

            // Gestion des services
            'manage services',

            // Gestion des réservations
            'manage reservations',

            // Gestion des avis
            'manage reviews',

            // Gestion des thèmes
            'manage themes',

            // Gestion des domaines
            'manage domains',

            // Gestion des créneaux horaires
            'manage timeslots',

            // Accès au profil
            'view profile',
            'update profile',

            // Autres permissions spécifiques
            'create reservations',
            'view reservations',
            'cancel reservations',
            'reschedule reservations',
        ];

        // Créer les permissions
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Créer les rôles et leur assigner des permissions

        // Rôle Owner
        $owner = Role::firstOrCreate(['name' => 'owner']);
        $owner->givePermissionTo(Permission::all());

        // Rôle Manager
        $manager = Role::firstOrCreate(['name' => 'manager']);
        $manager->givePermissionTo([
            'manage salons',
            'manage services',
            'manage reservations',
            'manage reviews',
            'manage themes',
            'manage domains',
            'manage timeslots',
            'view profile',
            'update profile',
        ]);

        // Rôle Stylist
        $stylist = Role::firstOrCreate(['name' => 'stylist']);
        $stylist->givePermissionTo([
            'manage reservations',
            'view profile',
            'update profile',
            'manage timeslots',
        ]);

        // Rôle Client
        $client = Role::firstOrCreate(['name' => 'client']);
        $client->givePermissionTo([
            'view profile',
            'update profile',
            'create reservations',
            'view reservations',
            'cancel reservations',
            'reschedule reservations',
        ]);
    }
}
